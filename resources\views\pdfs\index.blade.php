@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <h2>Upload a PDF</h2>

    @if(session('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif

    <form action="{{ route('pdfs.store') }}" method="POST" enctype="multipart/form-data" class="mb-5">
        @csrf
        <div class="form-group">
            <label>Title</label>
            <input name="title" class="form-control" required>
        </div>

        <div class="form-group">
            <label>Description</label>
            <textarea name="description" class="form-control"></textarea>
        </div>

        <div class="form-group">
            <label>PDF File</label>
            <input type="file" name="pdf_file" class="form-control-file" required>
        </div>

        <div class="form-group">
            <label>Thumbnail (Optional)</label>
            <input type="file" name="thumbnail" class="form-control-file">
        </div>

        <button class="btn btn-primary">Upload</button>
    </form>

    <h2>Uploaded PDFs</h2>
    <div class="row">
        @forelse($pdfs as $pdf)
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    @if($pdf->thumbnail_path)
                        <img src="{{ asset('storage/' . $pdf->thumbnail_path) }}" class="card-img-top" style="height: 200px; object-fit: cover;">
                    @endif
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $pdf->title }}</h5>
                        <p class="card-text">{{ $pdf->description }}</p>
                        <a href="{{ asset('storage/' . $pdf->pdf_path) }}" class="btn btn-sm btn-primary" target="_blank">View PDF</a>
                        {{-- <a href="{{ route('pdfs.show', $pdf->id) }}" class="btn btn-sm btn-outline-secondary mt-2">Share</a>
                        <a href="https://wa.me/?text={{ urlencode(route('pdfs.show', $pdf->id)) }}" target="_blank" class="btn btn-sm btn-success mt-2">Share via WhatsApp</a> --}}
                        <div class="mt-2">
                            <button onclick="copyToClipboard('{{ route('pdfs.show', $pdf->id) }}')" class="btn btn-sm btn-secondary">Copy Link</button>
                            <a href="https://wa.me/?text={{ urlencode(route('pdfs.show', $pdf->id)) }}" target="_blank" class="btn btn-sm btn-success">WhatsApp</a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('pdfs.show', $pdf->id)) }}" target="_blank" class="btn btn-sm btn-info">Facebook</a>
                            <a href="https://twitter.com/intent/tweet?url={{ urlencode(route('pdfs.show', $pdf->id)) }}" target="_blank" class="btn btn-sm btn-primary">Twitter</a>
                        </div>

                        <script>
                            function copyToClipboard(link) {
                                navigator.clipboard.writeText(link).then(() => {
                                    alert("Link copied!");
                                });
                            }
                        </script>
                    </div>
                </div>
            </div>
        @empty
            <p>No PDFs uploaded yet.</p>
        @endforelse
    </div>
</div>
@endsection
